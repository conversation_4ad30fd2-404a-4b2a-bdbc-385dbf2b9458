import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Share2, Facebook, Twitter, Instagram, Link, Copy, Check } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface SocialShareProps {
  animalName: string;
  animalId: string;
  animalStory?: string;
  animalImage?: string;
}

export function SocialShare({ animalName, animalId, animalStory, animalImage }: SocialShareProps) {
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();
  
  const shareUrl = `${window.location.origin}/animal/${animalId}`;
  const shareText = `Meet ${animalName}! ${animalStory ? animalStory.substring(0, 100) + '...' : 'This wonderful animal is looking for a loving home.'} Help us find ${animalName} a forever family! 🐾❤️`;
  
  const socialLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
    instagram: `https://www.instagram.com/`, // Instagram doesn't support direct sharing via URL
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast({
        title: "Link Copied!",
        description: "Share link has been copied to your clipboard.",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the link manually.",
        variant: "destructive"
      });
    }
  };

  const handleSocialShare = (platform: keyof typeof socialLinks) => {
    if (platform === 'instagram') {
      toast({
        title: "Instagram Sharing",
        description: "Please share manually on Instagram by posting the animal's photo and story.",
      });
      return;
    }
    
    window.open(socialLinks[platform], '_blank', 'width=600,height=400');
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Share2 className="mr-2 h-4 w-4" />
          Share {animalName}'s Story
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5 text-primary" />
            Share {animalName}'s Story
          </DialogTitle>
        </DialogHeader>
        
        <Card className="border-0 shadow-none">
          <CardContent className="space-y-4 p-0">
            <p className="text-sm text-muted-foreground">
              Help {animalName} find a forever home by sharing their story on social media!
            </p>

            {/* Share Link */}
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">Share Link</label>
              <div className="flex gap-2">
                <Input
                  value={shareUrl}
                  readOnly
                  className="text-sm"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="whitespace-nowrap"
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-success" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Social Media Buttons */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-foreground block">Share on Social Media</label>
              
              <Button
                variant="outline"
                className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                onClick={() => handleSocialShare('facebook')}
              >
                <Facebook className="mr-3 h-5 w-5" />
                Share on Facebook
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start text-blue-400 hover:text-blue-500 hover:bg-blue-50"
                onClick={() => handleSocialShare('twitter')}
              >
                <Twitter className="mr-3 h-5 w-5" />
                Share on Twitter
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start text-pink-600 hover:text-pink-700 hover:bg-pink-50"
                onClick={() => handleSocialShare('instagram')}
              >
                <Instagram className="mr-3 h-5 w-5" />
                Share on Instagram
              </Button>
            </div>

            {/* Pre-written Messages */}
            <div className="bg-muted/50 p-3 rounded-lg">
              <h4 className="text-sm font-semibold text-foreground mb-2">Suggested Post:</h4>
              <p className="text-sm text-muted-foreground italic">
                "{shareText}"
              </p>
            </div>

            {/* Additional Share Options */}
            <div className="pt-2 border-t">
              <p className="text-xs text-muted-foreground text-center">
                Every share helps {animalName} get one step closer to finding their forever home! 🏠💕
              </p>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}