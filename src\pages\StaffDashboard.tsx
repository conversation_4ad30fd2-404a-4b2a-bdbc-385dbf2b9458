import { Header } from "@/components/Header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom"; // If using react-router
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { 
  Plus, 
  Edit3, 
  Camera, 
  Upload, 
  MessageCircle, 
  Users, 
  PawPrint,
  Heart,
  Stethoscope,
  Calendar,
  FileText,
  Activity
} from "lucide-react";

const StaffDashboard = () => {
  const [selectedAnimal, setSelectedAnimal] = useState("");
  const [updateType, setUpdateType] = useState("");
  const [updateContent, setUpdateContent] = useState("");
  const [loading, setLoading] = useState(true);
const [userRole, setUserRole] = useState<string | null>(null);
const navigate = useNavigate();

  const animals = [
    { id: "1", name: "Luna", status: "Available" },
    { id: "2", name: "Whiskers", status: "Medical Care" },
    { id: "3", name: "Buddy", status: "Fostered" },
    { id: "4", name: "Mittens", status: "Available" },
  ];

  const recentUpdates = [
    {
      id: "1",
      animalName: "Luna",
      author: "Dr. Smith", 
      type: "Medical",
      content: "Completed vaccination series",
      date: "2 hours ago"
    },
    {
      id: "2",
      animalName: "Buddy",
      author: "Sarah M.",
      type: "Behavioral", 
      content: "Great progress with socialization",
      date: "4 hours ago"
    },
    {
      id: "3",
      animalName: "Whiskers", 
      author: "Mike R.",
      type: "General",
      content: "Enjoying playtime in the yard",
      date: "6 hours ago"
    }
  ];

  const pendingTasks = [
    { id: "1", task: "Schedule Luna's dental cleaning", priority: "High", due: "Today" },
    { id: "2", task: "Update Buddy's foster report", priority: "Medium", due: "Tomorrow" },
    { id: "3", task: "Post Whiskers' recovery photos", priority: "Low", due: "This week" },
  ];

  const handleSubmitUpdate = () => {
    if (selectedAnimal && updateType && updateContent) {
      // Here you would typically send this to your backend
      console.log("Submitting update:", { selectedAnimal, updateType, updateContent });
      
      // Reset form
      setSelectedAnimal("");
      setUpdateType("");
      setUpdateContent("");
      
      // Show success message (you could use toast here)
      alert("Update submitted successfully!");
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High": return "bg-destructive text-destructive-foreground";
      case "Medium": return "bg-warning text-warning-foreground";
      case "Low": return "bg-success text-success-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getUpdateTypeColor = (type: string) => {
    switch (type) {
      case "Medical": return "text-destructive";
      case "Behavioral": return "text-primary";
      case "General": return "text-success";
      default: return "text-muted-foreground";
    }
  };
  useEffect(() => {
  const checkRole = async () => {
    setLoading(true);
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      // Not logged in, redirect to login or home
      navigate("/login");
      return;
    }
    // Fetch user profile from your users table
    const { data, error } = await supabase
      .from("users")
      .select("role")
      .eq("id", user.id)
      .single();
    if (error || !data) {
      setUserRole(null);
      setLoading(false);
      return;
    }
    setUserRole(data.role);
    setLoading(false);
  };
  checkRole();
}, []);
if (loading) {
  return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
}
if (userRole !== "staff") {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You do not have permission to view this page.</p>
      </div>
    </div>
  );
}

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">Staff Dashboard</h1>
          <p className="text-xl text-muted-foreground">
            Manage animal profiles, post updates, and coordinate care
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="shadow-soft">
            <CardContent className="p-6 text-center">
              <div className="inline-flex p-3 rounded-full bg-primary/10 text-primary mb-4">
                <PawPrint className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">23</div>
              <div className="text-muted-foreground">Animals in Care</div>
            </CardContent>
          </Card>
          
          <Card className="shadow-soft">
            <CardContent className="p-6 text-center">
              <div className="inline-flex p-3 rounded-full bg-success/10 text-success mb-4">
                <Heart className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">8</div>
              <div className="text-muted-foreground">Available for Adoption</div>
            </CardContent>
          </Card>

          <Card className="shadow-soft">
            <CardContent className="p-6 text-center">
              <div className="inline-flex p-3 rounded-full bg-warning/10 text-warning mb-4">
                <Stethoscope className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">3</div>
              <div className="text-muted-foreground">In Medical Care</div>
            </CardContent>
          </Card>

          <Card className="shadow-soft">
            <CardContent className="p-6 text-center">
              <div className="inline-flex p-3 rounded-full bg-accent/10 text-accent-foreground mb-4">
                <Users className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">12</div>
              <div className="text-muted-foreground">In Foster Care</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs defaultValue="post-update" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="post-update">Post Update</TabsTrigger>
                <TabsTrigger value="add-animal">Add Animal</TabsTrigger>
                <TabsTrigger value="manage-media">Manage Media</TabsTrigger>
              </TabsList>

              <TabsContent value="post-update" className="mt-6">
                <Card className="shadow-medium">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageCircle className="h-5 w-5 text-primary" />
                      Post Animal Update
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="animal-select">Select Animal</Label>
                        <Select value={selectedAnimal} onValueChange={setSelectedAnimal}>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an animal" />
                          </SelectTrigger>
                          <SelectContent>
                            {animals.map((animal) => (
                              <SelectItem key={animal.id} value={animal.id}>
                                {animal.name} ({animal.status})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="update-type">Update Type</Label>
                        <Select value={updateType} onValueChange={setUpdateType}>
                          <SelectTrigger>
                            <SelectValue placeholder="Update category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="medical">Medical Update</SelectItem>
                            <SelectItem value="behavioral">Behavioral Progress</SelectItem>
                            <SelectItem value="general">General Update</SelectItem>
                            <SelectItem value="adoption">Adoption Update</SelectItem>
                            <SelectItem value="emergency">Emergency</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="update-content">Update Details</Label>
                      <Textarea
                        id="update-content"
                        placeholder="Share what's new with this animal - medical updates, behavioral progress, milestones, or any other important information..."
                        value={updateContent}
                        onChange={(e) => setUpdateContent(e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button 
                        onClick={handleSubmitUpdate}
                        disabled={!selectedAnimal || !updateType || !updateContent}
                        className="gradient-primary text-primary-foreground"
                      >
                        <MessageCircle className="mr-2 h-4 w-4" />
                        Post Update
                      </Button>
                      <Button variant="outline">
                        <Camera className="mr-2 h-4 w-4" />
                        Add Photos
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="add-animal" className="mt-6">
                <Card className="shadow-medium">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Plus className="h-5 w-5 text-primary" />
                      Add New Animal
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="animal-name">Animal Name</Label>
                        <Input id="animal-name" placeholder="Enter animal's name" />
                      </div>
                      <div>
                        <Label htmlFor="animal-type">Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select animal type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="dog">Dog</SelectItem>
                            <SelectItem value="cat">Cat</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="breed">Breed</Label>
                        <Input id="breed" placeholder="Animal breed" />
                      </div>
                      <div>
                        <Label htmlFor="age">Age</Label>
                        <Input id="age" placeholder="Estimated age" />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="rescue-story">Rescue Story</Label>
                      <Textarea
                        id="rescue-story"
                        placeholder="Tell this animal's rescue story..."
                        rows={4}
                      />
                    </div>

                    <Button className="gradient-primary text-primary-foreground">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Animal Profile
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="manage-media" className="mt-6">
                <Card className="shadow-medium">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Camera className="h-5 w-5 text-primary" />
                      Media Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="media-animal">Select Animal</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose animal for media upload" />
                        </SelectTrigger>
                        <SelectContent>
                          {animals.map((animal) => (
                            <SelectItem key={animal.id} value={animal.id}>
                              {animal.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground mb-4">
                        Drag and drop photos/videos here, or click to browse
                      </p>
                      <Button variant="outline">
                        <Upload className="mr-2 h-4 w-4" />
                        Choose Files
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Updates */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recent Updates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentUpdates.map((update) => (
                    <div key={update.id} className="border-l-2 border-primary/20 pl-3 py-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-foreground">{update.animalName}</span>
                        <Badge variant="outline" className={getUpdateTypeColor(update.type)}>
                          {update.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{update.content}</p>
                      <p className="text-xs text-muted-foreground">
                        by {update.author} • {update.date}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Pending Tasks */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  Pending Tasks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pendingTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-2 rounded border">
                      <div>
                        <p className="text-sm font-medium text-foreground">{task.task}</p>
                        <p className="text-xs text-muted-foreground">Due: {task.due}</p>
                      </div>
                      <Badge className={getPriorityColor(task.priority)}>
                        {task.priority}
                      </Badge>
                    </div>
                  ))}
                </div>
                <Button variant="outline" className="w-full mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Task
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Reports
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Users className="mr-2 h-4 w-4" />
                  Volunteer Schedule
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="mr-2 h-4 w-4" />
                  Medical Calendar
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default StaffDashboard;