import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Header } from "@/components/Header";
import { AnimalCard } from "@/components/AnimalCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, Heart, MapPin, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Animal } from "@/types/database";
import { toast } from "@/hooks/use-toast";

const AllAnimals = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnimals();
  }, []);

  const fetchAnimals = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('animals')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAnimals((data || []) as Animal[]);
    } catch (error: any) {
      toast({
        title: "Error loading animals",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Map database animals to the format expected by AnimalCard
  const mappedAnimals = animals.map(animal => ({
    id: animal.id,
    name: animal.name,
    type: animal.species === 'dog' ? 'Dog' : animal.species === 'cat' ? 'Cat' : 'Other',
    breed: animal.breed || 'Mixed breed',
    age: animal.age || 'Unknown age',
    status: animal.status === 'available' ? 'Available' as const :
            animal.status === 'adopted' ? 'Adopted' as const :
            animal.status === 'fostered' ? 'Fostered' as const :
            animal.status === 'medical_hold' ? 'Medical Care' as const :
            'Available' as const,
    image: animal.photos?.[0] || '/placeholder.svg',
    location: animal.rescue_location || 'Austin, TX',
    intakeDate: new Date(animal.intake_date).toLocaleDateString(),
    sponsorshipGoal: 500, // Default values - could be calculated from sponsorship data
    sponsorshipCurrent: 0,
    story: animal.intake_story || 'This wonderful animal is looking for their forever home.'
  }));

  const filteredAnimals = mappedAnimals.filter(animal => {
    const matchesSearch = animal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         animal.breed.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || animal.status === statusFilter;
    const matchesType = typeFilter === "all" || animal.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusCount = (status: string) => {
    if (status === "all") return mappedAnimals.length;
    return mappedAnimals.filter(animal => animal.status === status).length;
  };

  const statusOptions = [
    { value: "all", label: "All Animals", count: getStatusCount("all") },
    { value: "Available", label: "Available", count: getStatusCount("Available") },
    { value: "Fostered", label: "In Foster", count: getStatusCount("Fostered") },
    { value: "Medical Care", label: "Medical Care", count: getStatusCount("Medical Care") },
    { value: "Adopted", label: "Adopted", count: getStatusCount("Adopted") }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">All Our Animals</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Meet all the wonderful animals in our care. Each one is looking for love, 
            sponsorship, or their forever home.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {statusOptions.map((option) => (
            <Card 
              key={option.value} 
              className={`cursor-pointer transition-smooth hover:shadow-medium ${
                statusFilter === option.value ? 'ring-2 ring-primary border-primary' : ''
              }`}
              onClick={() => setStatusFilter(option.value)}
            >
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-foreground mb-1">{option.count}</div>
                <div className="text-sm text-muted-foreground">{option.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <Card className="mb-8 shadow-soft">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name or breed..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Animal Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Dog">Dogs</SelectItem>
                  <SelectItem value="Cat">Cats</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label} ({option.count})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" className="whitespace-nowrap">
                <Filter className="mr-2 h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <h2 className="text-2xl font-semibold text-foreground">
              {filteredAnimals.length} Animals Found
            </h2>
            {(searchTerm || statusFilter !== "all" || typeFilter !== "all") && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                  setTypeFilter("all");
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* Animals Grid */}
        {filteredAnimals.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAnimals.map((animal) => (
              <AnimalCard key={animal.id} {...animal} />
            ))}
          </div>
        ) : (
          <Card className="p-12 text-center shadow-soft">
            <div className="text-muted-foreground mb-4">
              <Heart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold mb-2">No animals found</h3>
              <p>Try adjusting your search criteria or filters.</p>
            </div>
            <Button 
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all"); 
                setTypeFilter("all");
              }}
            >
              View All Animals
            </Button>
          </Card>
        )}

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card className="gradient-hero p-8 shadow-strong">
            <h3 className="text-2xl font-bold text-white mb-4">
              Want to Help More Animals?
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              Every sponsorship, donation, and volunteer hour helps us save more lives. 
              Join our mission to give every animal a second chance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-primary hover:bg-white/90 font-semibold" asChild>
                <Link to="/animals">
                  <Heart className="mr-2 h-5 w-5" />
                  Start Sponsoring
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 font-semibold" asChild>
                <Link to="/auth">
                  Become a Volunteer
                </Link>
              </Button>
            </div>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default AllAnimals;