import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { MessageCircle, Camera, Send, User, Stethoscope, Heart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UpdateFormProps {
  animalId: string;
  animalName: string;
}

export function UpdateForm({ animalId, animalName }: UpdateFormProps) {
  const [updateType, setUpdateType] = useState("");
  const [updateContent, setUpdateContent] = useState("");
  const [authorName, setAuthorName] = useState("");
  const [authorRole, setAuthorRole] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!updateType || !updateContent || !authorName || !authorRole) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    // Here you would typically send this to your backend
    console.log("Submitting update:", {
      animalId,
      updateType,
      updateContent,
      authorName,
      authorRole,
      timestamp: new Date().toISOString()
    });

    // Reset form
    setUpdateType("");
    setUpdateContent("");
    setAuthorName("");
    setAuthorRole("");
    setIsOpen(false);

    toast({
      title: "Update Posted!",
      description: `Your update for ${animalName} has been posted successfully.`,
    });
  };

  const getUpdateTypeIcon = (type: string) => {
    switch (type) {
      case "medical": return <Stethoscope className="h-4 w-4" />;
      case "behavioral": return <Heart className="h-4 w-4" />;
      case "general": return <MessageCircle className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const getUpdateTypeColor = (type: string) => {
    switch (type) {
      case "medical": return "text-destructive";
      case "behavioral": return "text-primary";
      case "general": return "text-success";
      default: return "text-muted-foreground";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <MessageCircle className="mr-2 h-4 w-4" />
          Post Update
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-primary" />
            Post Update for {animalName}
          </DialogTitle>
        </DialogHeader>
        
        <Card className="border-0 shadow-none">
          <CardContent className="space-y-4 p-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="author-name">Your Name</Label>
                <Input
                  id="author-name"
                  placeholder="Enter your name"
                  value={authorName}
                  onChange={(e) => setAuthorName(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="author-role">Your Role</Label>
                <Select value={authorRole} onValueChange={setAuthorRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Foster Parent">Foster Parent</SelectItem>
                    <SelectItem value="Veterinarian">Veterinarian</SelectItem>
                    <SelectItem value="Volunteer">Volunteer</SelectItem>
                    <SelectItem value="Staff Member">Staff Member</SelectItem>
                    <SelectItem value="Caregiver">Caregiver</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="update-type">Update Type</Label>
              <Select value={updateType} onValueChange={setUpdateType}>
                <SelectTrigger>
                  <SelectValue placeholder="What kind of update is this?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="medical">
                    <div className="flex items-center gap-2">
                      <Stethoscope className="h-4 w-4 text-destructive" />
                      Medical Update
                    </div>
                  </SelectItem>
                  <SelectItem value="behavioral">
                    <div className="flex items-center gap-2">
                      <Heart className="h-4 w-4 text-primary" />
                      Behavioral Progress
                    </div>
                  </SelectItem>
                  <SelectItem value="general">
                    <div className="flex items-center gap-2">
                      <MessageCircle className="h-4 w-4 text-success" />
                      General Update
                    </div>
                  </SelectItem>
                  <SelectItem value="milestone">
                    <div className="flex items-center gap-2">
                      <Heart className="h-4 w-4 text-warning" />
                      Milestone Achievement
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="update-content">Update Details</Label>
              <Textarea
                id="update-content"
                placeholder={`Share what's new with ${animalName}... How are they doing? Any progress, milestones, or special moments to share?`}
                value={updateContent}
                onChange={(e) => setUpdateContent(e.target.value)}
                rows={4}
                className="resize-none"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Share specific details that supporters and potential adopters would love to hear!
              </p>
            </div>

            {/* Preview */}
            {(updateType || updateContent) && (
              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <h4 className="font-semibold text-sm text-muted-foreground mb-2">Preview:</h4>
                <div className="border-l-2 border-primary/20 pl-4 py-2">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-foreground">{authorName || "Your Name"}</span>
                      {authorRole && (
                        <Badge variant="outline" className={updateType ? getUpdateTypeColor(updateType) : ""}>
                          {authorRole}
                        </Badge>
                      )}
                    </div>
                    <span className="text-sm text-muted-foreground">Just now</span>
                  </div>
                  {updateContent && (
                    <p className="text-foreground">{updateContent}</p>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-3 pt-4">
              <Button 
                onClick={handleSubmit}
                disabled={!updateType || !updateContent || !authorName || !authorRole}
                className="gradient-primary text-primary-foreground flex-1"
              >
                <Send className="mr-2 h-4 w-4" />
                Post Update
              </Button>
              <Button variant="outline">
                <Camera className="mr-2 h-4 w-4" />
                Add Photos
              </Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}