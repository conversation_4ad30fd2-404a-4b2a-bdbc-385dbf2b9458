import { useState, useEffect } from 'react'
import { Head<PERSON> } from '@/components/Header'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { supabase } from '@/integrations/supabase/client'
import { User, Animal, AnimalUpdate, Sponsorship } from '@/types/database'

import { toast } from '@/hooks/use-toast'
import { Users, Heart, DollarSign, AlertCircle, Plus, Edit, Trash2, Upload } from 'lucide-react'

export const AdminDashboard = () => {
  const [users, setUsers] = useState<User[]>([])
  const [animals, setAnimals] = useState<Animal[]>([])
  const [updates, setUpdates] = useState<AnimalUpdate[]>([])
  const [sponsorships, setSponsorships] = useState<Sponsorship[]>([])
  const [animalPhotos, setAnimalPhotos] = useState<File[]>([]);
const [animalVideos, setAnimalVideos] = useState<File[]>([]);
  const [loading, setLoading] = useState(false)
  const [newAnimal, setNewAnimal] = useState<Partial<Animal>>({
    name: '',
    species: 'dog',
    breed: '',
    age: '',
    gender: 'unknown',
    size: 'medium',
    status: 'available',
    intake_date: new Date().toISOString().split('T')[0],
    rescue_location: '',
    intake_story: '',
    medical_notes: '',
    behavioral_notes: '',
    photos: [],
    videos: []
  })

  const [addStaffOpen, setAddStaffOpen] = useState(false);
const [newStaff, setNewStaff] = useState({
  full_name: '',
  email: '',
  phone: '',
  address: '',
  role: 'staff',
  password: '' // default role
});
  useEffect(() => {
    console.log("AdminDashboard mounted, calling fetchData");
    fetchData()
  }, [])

  const fetchData = async () => {
    console.log("fetchData started"); 
    try {
      // setLoading(true)
      
      // Fetch users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })
        
      
      if (usersError) throw usersError
      setUsers((usersData || []) as User[])
      console.log('usersData:', usersData, 'usersError:', usersError);

      // Fetch animals
      const { data: animalsData, error: animalsError } = await supabase
        .from('animals')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (animalsError) throw animalsError
      setAnimals((animalsData || []) as Animal[])
      console.log('animalsData:', animalsData, 'animalsError:', animalsError);

      // Fetch updates
      const { data: updatesData, error: updatesError } = await supabase
        .from('animal_updates')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50)
      
      if (updatesError) throw updatesError
      setUpdates((updatesData || []) as AnimalUpdate[])
      console.log('updatesData:', updatesData, 'updatesError:', updatesError);

      // Fetch sponsorships (only accessible to staff/admin)
      const { data: sponsorshipsData, error: sponsorshipsError } = await supabase
        .from('sponsorships')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (sponsorshipsError) {
        console.warn('Could not load sponsorships:', sponsorshipsError.message)
        // This is expected for non-staff users - continue with empty sponsorships
        setSponsorships([])
      } else {
        setSponsorships((sponsorshipsData || []) as Sponsorship[])
      }

      console.log('sponsorshipsData:', sponsorshipsData, 'sponsorshipsError:', sponsorshipsError);
    } catch (error: any) {
      toast({
        title: "Error loading data",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAnimal = async () => {
  try {
    // Validate required fields
    if (!newAnimal.name || !newAnimal.species) {
      toast({
        title: "Missing required fields",
        description: "Please fill in at least the name and species.",
        variant: "destructive",
      });
      return;
    }

    // 1. Upload photos
    let photoUrls = [];
    for (const file of animalPhotos) {
      const { data, error } = await supabase.storage
        .from('animal-media')
        .upload(`photos/${Date.now()}_${file.name}`, file);
      if (error) {
        toast({ title: "Photo upload failed", description: error.message, variant: "destructive" });
        return;
      }
      const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
      photoUrls.push(url);
    }

    // 2. Upload videos
    let videoUrls = [];
    for (const file of animalVideos) {
      const { data, error } = await supabase.storage
        .from('animal-media')
        .upload(`videos/${Date.now()}_${file.name}`, file);
      if (error) {
        toast({ title: "Video upload failed", description: error.message, variant: "destructive" });
        return;
      }
      const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
      videoUrls.push(url);
    }

    // 3. Insert animal with photo/video URLs
    const { error } = await supabase
      .from('animals')
      .insert([{
        ...newAnimal,
        photos: photoUrls,
        videos: videoUrls,
      }]);

    if (error) {
      console.error('Supabase error:', error);
      throw error;
    }

    toast({
      title: "Animal added successfully",
      description: `${newAnimal.name} has been added to the system.`,
    });

    setNewAnimal({
      name: '',
      species: 'dog',
      breed: '',
      age: '',
      gender: 'unknown',
      size: 'medium',
      status: 'available',
      intake_date: new Date().toISOString().split('T')[0],
      rescue_location: '',
      intake_story: '',
      medical_notes: '',
      behavioral_notes: '',
      photos: [],
      videos: []
    });
    setAnimalPhotos([]);
    setAnimalVideos([]);
    fetchData();
  } catch (error: any) {
    console.error('Error adding animal:', error);
    toast({
      title: "Error adding animal",
      description: error.message || "An unexpected error occurred",
      variant: "destructive",
    });
  }
};
  const handleDeleteAnimal = async (id: string) => {
    try {
      const { error } = await supabase
        .from('animals')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      
      toast({
        title: "Animal removed",
        description: "The animal has been removed from the system.",
      })
      
      fetchData()
    } catch (error: any) {
      toast({
        title: "Error removing animal",
        description: error.message,
        variant: "destructive",
      })
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'staff': return 'bg-blue-100 text-blue-800'
      case 'volunteer': return 'bg-green-100 text-green-800'
      case 'foster': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'adopted': return 'bg-blue-100 text-blue-800'
      case 'fostered': return 'bg-purple-100 text-purple-800'
      case 'medical_hold': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  const totalDonations = sponsorships.reduce((sum, s) => sum + s.amount, 0)
  const activeAnimals = animals.filter(a => ['available', 'fostered'].includes(a.status)).length
  const adoptedAnimals = animals.filter(a => a.status === 'adopted').length

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold gradient-text">Admin Dashboard</h1>
            <p className="text-muted-foreground">Manage users, animals, and rescue operations</p>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{users.length}</p>
                </div>
                <Users className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Animals</p>
                  <p className="text-2xl font-bold">{activeAnimals}</p>
                </div>
                <Heart className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Successfully Adopted</p>
                  <p className="text-2xl font-bold">{adoptedAnimals}</p>
                </div>
                <Heart className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Donations</p>
                  <p className="text-2xl font-bold">${totalDonations.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="animals" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="animals">Animals</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="updates">Recent Updates</TabsTrigger>
            <TabsTrigger value="sponsorships">Sponsorships</TabsTrigger>
          </TabsList>

          {/* Animals Tab */}
          <TabsContent value="animals" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Animal Management</h2>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="gradient-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Animal
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Add New Animal</DialogTitle>
                  </DialogHeader>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={newAnimal.name}
                        onChange={(e) => setNewAnimal({...newAnimal, name: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="species">Species</Label>
                      <Select 
                        value={newAnimal.species} 
                        onValueChange={(value) => setNewAnimal({...newAnimal, species: value as 'dog' | 'cat' | 'other'})}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dog">Dog</SelectItem>
                          <SelectItem value="cat">Cat</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="breed">Breed</Label>
                      <Input
                        id="breed"
                        value={newAnimal.breed}
                        onChange={(e) => setNewAnimal({...newAnimal, breed: e.target.value})}
                      />
                    </div>
                    {/* ...other fields... */}
<div className="col-span-2">
  <Label htmlFor="animal-photos">Photos</Label>
  <Input
    id="animal-photos"
    type="file"
    accept="image/*"
    multiple
    onChange={e => setAnimalPhotos(Array.from(e.target.files || []))}
  />
  <div className="flex flex-wrap gap-2 mt-2">
    {animalPhotos.map((file, idx) => (
      <span key={idx} className="text-xs bg-muted px-2 py-1 rounded">{file.name}</span>
    ))}
  </div>
</div>
<div className="col-span-2">
  <Label htmlFor="animal-videos">Videos</Label>
  <Input
    id="animal-videos"
    type="file"
    accept="video/*"
    multiple
    onChange={e => setAnimalVideos(Array.from(e.target.files || []))}
  />
  <div className="flex flex-wrap gap-2 mt-2">
    {animalVideos.map((file, idx) => (
      <span key={idx} className="text-xs bg-muted px-2 py-1 rounded">{file.name}</span>
    ))}
  </div>
</div>
                    <div>
                      <Label htmlFor="age">Age</Label>
                      <Input
                        id="age"
                        value={newAnimal.age}
                        onChange={(e) => setNewAnimal({...newAnimal, age: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="gender">Gender</Label>
                      <Select 
                        value={newAnimal.gender} 
                        onValueChange={(value) => setNewAnimal({...newAnimal, gender: value as 'male' | 'female' | 'unknown'})}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                          <SelectItem value="unknown">Unknown</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="size">Size</Label>
                      <Select 
                        value={newAnimal.size} 
                        onValueChange={(value) => setNewAnimal({...newAnimal, size: value as 'small' | 'medium' | 'large'})}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="intake_date">Intake Date</Label>
                      <Input
                        id="intake_date"
                        type="date"
                        value={newAnimal.intake_date}
                        onChange={(e) => setNewAnimal({...newAnimal, intake_date: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="rescue_location">Rescue Location</Label>
                      <Input
                        id="rescue_location"
                        value={newAnimal.rescue_location}
                        onChange={(e) => setNewAnimal({...newAnimal, rescue_location: e.target.value})}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="intake_story">Intake Story</Label>
                      <Textarea
                        id="intake_story"
                        value={newAnimal.intake_story}
                        onChange={(e) => setNewAnimal({...newAnimal, intake_story: e.target.value})}
                        rows={3}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="medical_notes">Medical Notes</Label>
                      <Textarea
                        id="medical_notes"
                        value={newAnimal.medical_notes}
                        onChange={(e) => setNewAnimal({...newAnimal, medical_notes: e.target.value})}
                        rows={2}
                      />
                    </div>
                  </div>
                  <Button onClick={handleCreateAnimal} className="w-full gradient-primary">
                    Add Animal
                  </Button>
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid gap-4">
              {animals.map((animal) => (
                <Card key={animal.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{animal.name}</h3>
                          <Badge className={getStatusColor(animal.status)}>
                            {animal.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-muted-foreground">
                          <span>{animal.species} • {animal.breed}</span>
                          <span>{animal.age} • {animal.gender}</span>
                          <span>Size: {animal.size}</span>
                          <span>Intake: {new Date(animal.intake_date).toLocaleDateString()}</span>
                        </div>
                        {animal.rescue_location && (
                          <p className="text-sm text-muted-foreground mt-1">
                            Rescued from: {animal.rescue_location}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDeleteAnimal(animal.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
  <h2 className="text-2xl font-semibold">User Management</h2>
  <Button onClick={() => setAddStaffOpen(true)} className="gradient-primary">
    <Plus className="h-4 w-4 mr-2" />
    Add Staff
  </Button>
</div>

<Dialog open={addStaffOpen} onOpenChange={setAddStaffOpen}>
  <DialogContent className="max-w-md">
    <DialogHeader>
      <DialogTitle>Add New Staff</DialogTitle>
    </DialogHeader>
    <div className="space-y-4">
      <div>
        <Label htmlFor="staff-name">Full Name</Label>
        <Input
          id="staff-name"
          value={newStaff.full_name}
          onChange={e => setNewStaff({ ...newStaff, full_name: e.target.value })}
        />
      </div>
      <div>
        <Label htmlFor="staff-email">Email</Label>
        <Input
          id="staff-email"
          type="email"
          value={newStaff.email}
          onChange={e => setNewStaff({ ...newStaff, email: e.target.value })}
        />
      </div>
      <div>
        <Label htmlFor="staff-phone">Phone</Label>
        <Input
          id="staff-phone"
          value={newStaff.phone}
          onChange={e => setNewStaff({ ...newStaff, phone: e.target.value })}
        />
      </div>
      <div>
        <Label htmlFor="staff-address">Address</Label>
        <Input
          id="staff-address"
          value={newStaff.address}
          onChange={e => setNewStaff({ ...newStaff, address: e.target.value })}
        />
      </div>
      <div>
  <Label htmlFor="staff-password">Password</Label>
  <Input
    id="staff-password"
    type="password"
    value={newStaff.password || ""}
    onChange={e => setNewStaff({ ...newStaff, password: e.target.value })}
  />
</div>
      <div>
        <Label htmlFor="staff-role">Role</Label>
        <Select
          value={newStaff.role}
          onValueChange={value => setNewStaff({ ...newStaff, role: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="staff">Staff</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="volunteer">Volunteer</SelectItem>
            <SelectItem value="foster">Foster</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <Button className="w-full gradient-primary" onClick={async () => {
  // Validation
  if (!newStaff.full_name || !newStaff.email || !newStaff.password) {
    toast({
      title: "Missing required fields",
      description: "Name, email, and password are required.",
      variant: "destructive",
    });
    return;
  }

  // 1. Create user in Supabase Auth
  // NOTE: This only works if you are using a service role key (NOT recommended in frontend for production)
  const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
    email: newStaff.email,
    password: newStaff.password,
    email_confirm: true,
  });

  if (authError) {
    toast({
      title: "Error creating user",
      description: authError.message,
      variant: "destructive",
    });
    return;
  }

  // 2. Insert profile in users table
  const { error: profileError } = await supabase.from('users').insert([{
    id: authUser.user.id, // link to auth user
    full_name: newStaff.full_name,
    email: newStaff.email,
    phone: newStaff.phone,
    address: newStaff.address,
    role: newStaff.role,
  }]);

  if (profileError) {
    toast({
      title: "Error adding staff profile",
      description: profileError.message,
      variant: "destructive",
    });
    return;
  }

  toast({
    title: "Staff added",
    description: `${newStaff.full_name} added successfully.`,
  });
  setAddStaffOpen(false);
  setNewStaff({
    full_name: '',
    email: '',
    phone: '',
    address: '',
    role: 'staff',
    password: '',
  });
  fetchData(); // Refresh user list
}}>
  Add Staff
</Button>
    </div>
  </DialogContent>
</Dialog>
       
            <div className="grid gap-4">
              {users.map((user) => (
                <Card key={user.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{user.full_name || 'No name'}</h3>
                          <Badge className={getRoleColor(user.role)}>
                            {user.role}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">{user.email}</p>
                        {user.phone && <p className="text-sm text-muted-foreground">{user.phone}</p>}
                        {user.address && <p className="text-sm text-muted-foreground">{user.address}</p>}
                        <p className="text-xs text-muted-foreground mt-2">
                          Joined: {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Updates Tab */}
          <TabsContent value="updates" className="space-y-6">
            <h2 className="text-2xl font-semibold">Recent Updates</h2>
            <div className="grid gap-4">
              {updates.map((update) => (
                <Card key={update.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Badge>{update.update_type}</Badge>
                        <span className="text-sm text-muted-foreground">
                          by {update.author_name} ({update.author_role})
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(update.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-foreground">{update.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Sponsorships Tab */}
          <TabsContent value="sponsorships" className="space-y-6">
            <h2 className="text-2xl font-semibold">Sponsorship Management</h2>
            <div className="grid gap-4">
              {sponsorships.map((sponsorship) => (
                <Card key={sponsorship.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold capitalize">
                            {sponsorship.sponsor_type.replace('_', ' ')} Sponsorship
                          </h3>
                          <Badge className={sponsorship.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                            {sponsorship.status}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">
                          ${sponsorship.amount} of ${sponsorship.target_amount} raised
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(sponsorship.amount / sponsorship.target_amount) * 100}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          Started: {new Date(sponsorship.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default AdminDashboard