import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, PawPrint, Users, Award } from "lucide-react";
import heroImage from "@/assets/hero-cat-img.jpg";

export function HeroSection() {
  return (
    <section className="relative min-h-[80vh] flex items-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <img 
          src={heroImage} 
          alt="Rescued dog looking lovingly at camera"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-4 py-20">
        <div className="max-w-2xl">
          <Badge className="mb-4 bg-primary/20 text-primary-foreground border-primary/30">
            <Heart className="h-3 w-3 mr-1" />
            Life-Saving Mission
          </Badge>
          
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
            Every Animal
            <span className="text-primary block">
              Has a Story
            </span>
          </h1>
          
          <p className="text-xl text-white/90 mb-8 leading-relaxed">
            Follow the rescue journeys, sponsor care, and help us save more lives. 
            Each animal in our care has a unique story of hope, healing, and love.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 mb-12">
            <Button size="lg" className="gradient-primary text-primary-foreground font-semibold text-lg px-8 py-3">
              <Heart className="mr-2 h-5 w-5" />
              Sponsor an Animal
            </Button>
            <Button size="lg" variant="outline" className="bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white/20 font-semibold text-lg px-8 py-3">
              View Rescue Stories
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <PawPrint className="h-8 w-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-white">247</div>
              <div className="text-sm text-white/80">Animals Rescued</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-white">89</div>
              <div className="text-sm text-white/80">Forever Homes</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Award className="h-8 w-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-white">156</div>
              <div className="text-sm text-white/80">Active Sponsors</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}