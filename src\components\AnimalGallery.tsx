import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { ChevronLeft, ChevronRight, Play, Camera, Heart, Share2 } from "lucide-react";

interface MediaItem {
  id: string;
  type: "photo" | "video";
  url: string;
  caption: string;
  date: string;
}

interface AnimalGalleryProps {
  animalName: string;
  media: MediaItem[];
}

export function AnimalGallery({ animalName, media }: AnimalGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const nextMedia = () => {
    setCurrentIndex((prev) => (prev + 1) % media.length);
  };
  
  const prevMedia = () => {
    setCurrentIndex((prev) => (prev - 1 + media.length) % media.length);
  };

  const currentMedia = media[currentIndex];

  return (
    <Card className="overflow-hidden shadow-medium">
      {/* Main Media Display */}
      <div className="relative aspect-video bg-muted">
        {currentMedia?.type === "photo" ? (
          <img 
            src={currentMedia.url} 
            alt={`${animalName} - ${currentMedia.caption}`}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="relative w-full h-full bg-black flex items-center justify-center">
            <video 
              src={currentMedia?.url} 
              className="w-full h-full object-cover"
              controls
            />
       
          </div>
        )}
        
        {/* Media Controls */}
        <div className="absolute inset-0 flex items-center justify-between p-4 opacity-0 hover:opacity-100 transition-smooth">
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={prevMedia}
            className="bg-black/50 text-white hover:bg-black/70"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={nextMedia}
            className="bg-black/50 text-white hover:bg-black/70"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Media Type Badge */}
        <Badge className="absolute top-3 left-3 bg-black/70 text-white">
          {currentMedia?.type === "video" ? (
            <><Play className="h-3 w-3 mr-1" />Video</>
          ) : (
            <><Camera className="h-3 w-3 mr-1" />Photo</>
          )}
        </Badge>

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 flex gap-2">
          <Button size="sm" variant="secondary" className="bg-black/50 text-white hover:bg-black/70">
            <Heart className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="secondary" className="bg-black/50 text-white hover:bg-black/70">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Media Info */}
      <div className="p-4">
        <h4 className="font-semibold text-foreground mb-1">{currentMedia?.caption}</h4>
        <p className="text-sm text-muted-foreground mb-4">{currentMedia?.date}</p>
        
        {/* Media Counter */}
        <div className="flex justify-center mb-4">
          <span className="text-sm text-muted-foreground">
            {currentIndex + 1} of {media.length}
          </span>
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="px-4 pb-4">
        <div className="grid grid-cols-6 gap-2">
          {media.map((item, index) => (
            <button
              key={item.id}
              onClick={() => setCurrentIndex(index)}
              className={`aspect-square rounded-md overflow-hidden border-2 transition-smooth ${
                index === currentIndex ? 'border-primary' : 'border-transparent hover:border-muted-foreground'
              }`}
            >
              <img 
                src={item.url} 
                alt={`${animalName} thumbnail`}
                className="w-full h-full object-cover"
              />
             
            </button>
          ))}
        </div>
      </div>
    </Card>
  );
}