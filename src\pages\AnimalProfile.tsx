import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useSponsorships } from "@/hooks/useSponsorships";
import { Header } from "@/components/Header";
import { AnimalGallery } from "@/components/AnimalGallery";
import { SponsorshipOptions } from "@/components/SponsorshipOptions";
import { UpdateForm } from "@/components/UpdateForm";
import { SocialShare } from "@/components/SocialShare";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Heart, 
  Share2, 
  MapPin, 
  Calendar, 
  Clock, 
  User, 
  Stethoscope,
  ChevronLeft,
  MessageCircle,
  Camera,
  Edit3
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Animal, AnimalUpdate } from "@/types/database";
import { toast } from "@/hooks/use-toast";

const AnimalProfile = () => {
  const { id } = useParams();
  const { totalRaised, refetch: refetchSponsorships } = useSponsorships(id || "");
  const [currentAnimal, setCurrentAnimal] = useState<Animal | null>(null);
  const [updates, setUpdates] = useState<AnimalUpdate[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchAnimalData();
    }
  }, [id]);

  const fetchAnimalData = async () => {
    try {
      setLoading(true);
      
      // Fetch animal data
      const { data: animalData, error: animalError } = await supabase
        .from('animals')
        .select('*')
        .eq('id', id)
        .single();

      if (animalError) {
        console.error('Error fetching animal:', animalError);
        toast({
          title: "Animal not found",
          description: "The animal you're looking for doesn't exist.",
          variant: "destructive",
        });
        return;
      }

      setCurrentAnimal(animalData as Animal);

      // Fetch updates for this animal
      const { data: updatesData, error: updatesError } = await supabase
        .from('animal_updates')
        .select('*')
        .eq('animal_id', id)
        .order('created_at', { ascending: false });

      if (updatesError) {
        console.error('Error fetching updates:', updatesError);
      } else {
        setUpdates((updatesData || []) as AnimalUpdate[]);
      }

    } catch (error: any) {
      console.error('Error fetching animal data:', error);
      toast({
        title: "Error loading animal data",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available": return "bg-success text-success-foreground";
      case "Fostered": return "bg-warning text-warning-foreground";
      case "Adopted": return "bg-primary text-primary-foreground";
      case "Medical Care": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getUpdateTypeColor = (type: string) => {
    switch (type) {
      case "medical": return "text-success";
      case "behavioral": return "text-destructive";
      case "general": return "text-primary";
      case "milestone": return "text-warning";
      default: return "text-muted-foreground";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!currentAnimal) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Animal Not Found</h1>
          <p className="text-muted-foreground mb-4">The animal you're looking for doesn't exist.</p>
          <Button asChild>
            <Link to="/animals">View All Animals</Link>
          </Button>
        </div>
      </div>
    );
  }

  const sponsorshipGoal = 500; // Default sponsorship goal
  const sponsorshipCurrent = totalRaised;
  const sponsorshipPercentage = (sponsorshipCurrent / sponsorshipGoal) * 100;

  // Map database animal to display format
  const animalDisplay = {
    id: currentAnimal.id,
    name: currentAnimal.name,
    type: currentAnimal.species === 'dog' ? 'Dog' : currentAnimal.species === 'cat' ? 'Cat' : 'Other',
    breed: currentAnimal.breed || 'Mixed breed',
    age: currentAnimal.age || 'Unknown age',
    weight: 'N/A', // Not in database schema
    status: currentAnimal.status === 'available' ? 'Available' as const :
            currentAnimal.status === 'adopted' ? 'Adopted' as const :
            currentAnimal.status === 'fostered' ? 'Fostered' as const :
            currentAnimal.status === 'medical_hold' ? 'Medical Care' as const :
            'Available' as const,
    gender: currentAnimal.gender === 'male' ? 'Male' : 
            currentAnimal.gender === 'female' ? 'Female' : 'Unknown',
    color: 'N/A', // Not in database schema
    location: currentAnimal.rescue_location || 'Austin, TX',
    intakeDate: new Date(currentAnimal.intake_date).toLocaleDateString(),
    rescueStory: currentAnimal.intake_story || 'This wonderful animal is looking for their forever home.',
    medicalHistory: currentAnimal.medical_notes || 'Medical information not available.',
    personality: currentAnimal.behavioral_notes || 'A loving animal looking for a home.',
    sponsorshipGoal,
    sponsorshipCurrent,
    currentNeeds: ['Monthly food supply', 'Veterinary care', 'Toys and enrichment'],
    fosterer: 'Care Team',
media: (() => {
  const arr = [
    ...(currentAnimal.photos?.map((photo, index) => ({
      id: `photo-${index + 1}`,
      type: 'photo' as const,
      url: photo || '/placeholder.svg',
      caption: `${currentAnimal.name} - Photo ${index + 1}`,
      date: new Date(currentAnimal.created_at).toLocaleDateString()
    })) || []),
    ...(currentAnimal.videos?.map((video, index) => ({
      id: `video-${index + 1}`,
      type: 'video' as const,
      url: video,
      caption: `${currentAnimal.name} - Video ${index + 1}`,
      date: new Date(currentAnimal.created_at).toLocaleDateString()
    })) || [])
  ];
  return arr.length > 0 ? arr : [{
    id: "1",
    type: 'photo' as const,
    url: '/placeholder.svg',
    caption: `${currentAnimal.name} - No media available`,
    date: new Date(currentAnimal.created_at).toLocaleDateString()
  }];
})(),
    updates: updates.map(update => ({
      id: update.id,
      date: new Date(update.created_at).toLocaleDateString(),
      author: update.author_name,
      role: update.author_role,
      content: update.content,
      type: update.update_type
    }))
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 mb-6 text-sm text-muted-foreground">
          <Link to="/" className="hover:text-primary transition-smooth">Home</Link>
          <span>/</span>
          <Link to="/animals" className="hover:text-primary transition-smooth">Animals</Link>
          <span>/</span>
          <span className="text-foreground">{animalDisplay.name}</span>
        </div>

        {/* Back Button */}
        <Button variant="outline" className="mb-6" asChild>
          <Link to="/">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Animals
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Animal Header */}
            <Card className="shadow-medium">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                     <CardTitle className="text-3xl font-bold text-foreground">{animalDisplay.name}</CardTitle>
                     <Badge className={getStatusColor(animalDisplay.status)}>
                       {animalDisplay.status}
                      </Badge>
                    </div>
                    <p className="text-lg text-muted-foreground">
                      {animalDisplay.breed} • {animalDisplay.age} • {animalDisplay.gender}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Weight:</span>
                     <p className="font-medium">{animalDisplay.weight}</p>
                   </div>
                   <div>
                     <span className="text-muted-foreground">Color:</span>
                     <p className="font-medium">{animalDisplay.color}</p>
                   </div>
                   <div>
                     <span className="text-muted-foreground">Location:</span>
                     <p className="font-medium">{animalDisplay.location}</p>
                   </div>
                   <div>
                     <span className="text-muted-foreground">Intake Date:</span>
                     <p className="font-medium">{animalDisplay.intakeDate}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Photo/Video Gallery */}
            <AnimalGallery animalName={animalDisplay.name} media={animalDisplay.media} />

            {/* Tabs for Details */}
            <Tabs defaultValue="story" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="story">Rescue Story</TabsTrigger>
                <TabsTrigger value="personality">Personality</TabsTrigger>
                <TabsTrigger value="medical">Medical Info</TabsTrigger>
                <TabsTrigger value="updates">Updates</TabsTrigger>
              </TabsList>

              <TabsContent value="story" className="mt-6">
                <Card className="shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-primary" />
                      {animalDisplay.name}'s Rescue Story
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-foreground leading-relaxed">{animalDisplay.rescueStory}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="personality" className="mt-6">
                <Card className="shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5 text-primary" />
                      Personality & Traits
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <p className="text-foreground mb-4">{animalDisplay.personality}</p>
                     <div>
                       <h4 className="font-semibold mb-2">Current Needs:</h4>
                       <ul className="list-disc list-inside space-y-1">
                         {animalDisplay.currentNeeds.map((need, index) => (
                          <li key={index} className="text-muted-foreground">{need}</li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="medical" className="mt-6">
                <Card className="shadow-soft">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Stethoscope className="h-5 w-5 text-primary" />
                      Medical History
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-foreground">{animalDisplay.medicalHistory}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="updates" className="mt-6">
                <Card className="shadow-soft">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <MessageCircle className="h-5 w-5 text-primary" />
                        Recent Updates
                      </CardTitle>
                      <Button size="sm" variant="outline">
                        <Edit3 className="h-4 w-4 mr-2" />
                        Add Update
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4">
                       {animalDisplay.updates.length > 0 ? (
                         animalDisplay.updates.map((update) => (
                        <div key={update.id} className="border-l-2 border-primary/20 pl-4 py-2">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-foreground">{update.author}</span>
                              <Badge variant="outline" className={getUpdateTypeColor(update.type)}>
                                {update.role}
                              </Badge>
                            </div>
                            <span className="text-sm text-muted-foreground">{update.date}</span>
                          </div>
                          <p className="text-foreground">{update.content}</p>
                        </div>
                         ))
                       ) : (
                         <p className="text-muted-foreground">No updates available for this animal yet.</p>
                       )}
                     </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Foster Information */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  Current Foster
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-semibold text-foreground">{animalDisplay.fosterer}</p>
                <p className="text-sm text-muted-foreground">Experienced foster parent</p>
              </CardContent>
            </Card>

            {/* Sponsorship Progress */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-primary" />
                  Sponsorship Progress
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">${animalDisplay.sponsorshipCurrent} / ${animalDisplay.sponsorshipGoal}</span>
                  </div>
                  <Progress value={sponsorshipPercentage} className="h-3" />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(sponsorshipPercentage)}% funded
                  </p>
                </div>
                <Button 
                  className="w-full gradient-primary text-primary-foreground"
                  onClick={() => {
                    // Scroll to sponsorship options
                    const sponsorshipOptions = document.querySelector('[data-sponsorship-options]');
                    if (sponsorshipOptions) {
                      sponsorshipOptions.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  <Heart className="mr-2 h-4 w-4" />
                  Sponsor {animalDisplay.name}
                </Button>
              </CardContent>
            </Card>

            {/* Sponsorship Options */}
             <SponsorshipOptions 
               animalName={animalDisplay.name} 
               animalId={animalDisplay.id}
              onSponsorshipSuccess={refetchSponsorships}
            />

            {/* Quick Actions */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                 <SocialShare 
                   animalName={animalDisplay.name}
                   animalId={animalDisplay.id}
                   animalStory={animalDisplay.rescueStory}
                   animalImage={animalDisplay.media[0]?.url}
                />
                <Button variant="outline" className="w-full">
                  <Camera className="mr-2 h-4 w-4" />
                  Submit Photos
                </Button>
                 <UpdateForm 
                   animalId={animalDisplay.id}
                   animalName={animalDisplay.name}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AnimalProfile;